// API Endpoints Configuration
export const ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    SEND_OTP: 'auth/login/email/otp',
    VERIFY_OTP: '/auth/login/email/verify',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile'
  },

  // Jobs
  JOBS: {
    LIST: '/jobs',
    DETAILS: (id) => `/jobs/${id}`,
    ENUMS: '/jobs/job-enums',
    INDUSTRIES: '/industries',
    TOGGLE_JOB_FAVORITE: (jobId) => `/job-applications/jobs/${jobId}/favorite`,
    SAVED: '/job-applications/jobs/saved', // <-- Add this line
  },

  // Applications
  APPLICATIONS: {
    LIST: '/applications',
    DETAILS: (id) => `/applications/${id}`,
    CREATE: (jobId) => `/job-applications/jobs/${jobId}/apply`,
    UPDATE: (id) => `/applications/${id}`,
    DELETE: (id) => `/applications/${id}`,
    BY_JOB: (jobId) => `/jobs/${jobId}/applications`,
    BY_USER: '/job-applications/my-applications',
    WITHDRAW: (id) => `/job-applications/${id}/withdraw`
  },

  // User Profile
  PROFILE: {
    GET: '/profile',
    UPDATE: '/profile',
    UPLOAD_AVATAR: '/profile/avatar',
    DELETE_AVATAR: '/profile/avatar',
    PRIVACY_SETTINGS: '/profile/privacy'
  },

  // Reference Data
  REFERENCE: {
    DEPARTMENTS: '/reference/departments',
    JOB_STATUSES: '/reference/job-statuses',
    JOB_TYPES: '/reference/job-types',
    EXPERIENCE_LEVELS: '/reference/experience-levels',
    SKILLS: '/reference/skills',
    LOCATIONS: '/reference/locations'
  },

  // Dashboard - Job Seeker specific endpoint
  DASHBOARD: {
    STATS: '/jobs/jobseeker/dashboard', // Main dashboard endpoint
    RECENT_ACTIVITY: '/dashboard/activity',
    RECOMMENDATIONS: '/dashboard/recommendations'
  },

  // Companies
  COMPANIES: {
    LIST: '/companies',
    DETAILS: (id) => `/companies/${id}`,
    JOBS: (id) => `/companies/${id}/jobs`
  },

  // Notifications
  NOTIFICATIONS: {
    LIST: '/notifications',
    MARK_READ: (id) => `/notifications/read/${id}`,
    MARK_ALL_READ: '/notifications/mark-all-read',
    SETTINGS: '/notifications/settings'
  },

  // File Upload
  UPLOAD: {
    RESUME: '/upload/resume',
    AVATAR: '/upload/avatar',
    DOCUMENTS: '/upload/documents'
  }
}